"use client"

import React from 'react';

const Page = () => {
    return (
            <div className="p-6">
                <div className="mb-6">
                    <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
                    <p className="text-gray-600 mt-1">Overview of your hospital management system</p>
                </div>

                {/* Dashboard content */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    <div className="lg:col-span-2">
                        <div className="bg-white rounded-lg shadow-sm border p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h3>
                            <div className="space-y-4">
                                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                                    <span className="text-sm text-gray-700">New appointment scheduled for Dr<PERSON> <PERSON></span>
                                    <span className="text-xs text-gray-500 ml-auto">2 min ago</span>
                                </div>
                                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                    <span className="text-sm text-gray-700">Patient John Doe checked in</span>
                                    <span className="text-xs text-gray-500 ml-auto">5 min ago</span>
                                </div>
                                <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                    <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                    <span className="text-sm text-gray-700">Room 205 maintenance scheduled</span>
                                    <span className="text-xs text-gray-500 ml-auto">10 min ago</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div>
                        <div className="bg-white rounded-lg shadow-sm border p-6">
                            <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Stats</h3>
                            <div className="space-y-4">
                                <div className="text-center p-4 bg-blue-50 rounded-lg">
                                    <div className="text-2xl font-bold text-blue-600">24</div>
                                    <div className="text-sm text-blue-600">Appointments Today</div>
                                </div>
                                <div className="text-center p-4 bg-green-50 rounded-lg">
                                    <div className="text-2xl font-bold text-green-600">12</div>
                                    <div className="text-sm text-green-600">Available Doctors</div>
                                </div>
                                <div className="text-center p-4 bg-yellow-50 rounded-lg">
                                    <div className="text-2xl font-bold text-yellow-600">8</div>
                                    <div className="text-sm text-yellow-600">Free Rooms</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

    );
};

export default Page;