import NextAuth from "next-auth"
import Cred<PERSON><PERSON><PERSON>rovider from "next-auth/providers/credentials"

const handler = NextAuth({
  providers: [
    CredentialsProvider({
      name: "credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        // This is where you would validate credentials against your database
        // For demo purposes, we'll use hardcoded credentials
        if (credentials?.email === "<EMAIL>" && credentials?.password === "admin123") {
          return {
            id: "1",
            email: "<EMAIL>",
            name: "Admin User",
            role: "admin"
          }
        }
        
        if (credentials?.email === "<EMAIL>" && credentials?.password === "doctor123") {
          return {
            id: "2",
            email: "<EMAIL>",
            name: "Dr<PERSON> <PERSON>",
            role: "doctor"
          }
        }
        
        // Return null if user data could not be retrieved
        return null
      }
    })
  ],
  pages: {
    signIn: '/auth/login',
    signOut: '/auth/login',
  },
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub
        session.user.role = token.role
      }
      return session
    },
  },
  session: {
    strategy: "jwt",
  },
  secret: process.env.NEXTAUTH_SECRET,
})

export { handler as GET, handler as POST }
