import type { Metada<PERSON> } from "next";
import { <PERSON>eist, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import SessionProvider from "@/components/SessionProvider";
import ConditionalLayout from "@/components/ConditionalLayout";
import Script from "next/script";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
      <html lang="en">
      <Script src="https://kit.fontawesome.com/d4a083258e.js" crossOrigin="anonymous"></Script>
      <body
          className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
      <SessionProvider>
        <ConditionalLayout>
          {children}
        </ConditionalLayout>
      </SessionProvider>
      </body>
      </html>
  );
}
