"use client"

import React from 'react';
import CreateButton from "@/components/CreateButton";
import {FA_Icons} from "@/icons/icons";

const Page = () => {
    const doctors = [
        { id: 1, name: "Dr. <PERSON>", specialty: "Cardiology", status: "Available", patients: 12 },
        { id: 2, name: "Dr. <PERSON>", specialty: "Neurology", status: "Busy", patients: 8 },
        { id: 3, name: "Dr. <PERSON>", specialty: "Orthopedics", status: "Available", patients: 15 },
        { id: 4, name: "Dr. <PERSON>", specialty: "Pediatrics", status: "On Break", patients: 6 },
    ];

    return (
            <div className="p-6">
                <div className="mb-6 flex justify-between items-center">
                    <div>
                        <h1 className="text-2xl font-bold text-gray-900">Doctors</h1>
                        <p className="text-gray-600 mt-1">Manage hospital doctors and their schedules</p>
                    </div>

                        <CreateButton
                            suffixSting={"Doctor"}
                            icon ={FA_Icons.USERS}
                        />
                </div>

                {/* Doctors Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {doctors.map((doctor) => (
                        <div key={doctor.id} className="bg-white rounded-lg shadow-sm border p-6">
                            <div className="flex items-center space-x-4 mb-4">
                                <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white font-semibold">
                                    {doctor.name.split(' ').map(n => n[0]).join('')}
                                </div>
                                <div>
                                    <h3 className="font-medium text-gray-900">{doctor.name}</h3>
                                    <p className="text-sm text-gray-500">{doctor.specialty}</p>
                                </div>
                            </div>

                            <div className="space-y-2">
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-600">Status:</span>
                                    <span className={`text-xs px-2 py-1 rounded-full ${
                                        doctor.status === 'Available' ? 'bg-green-100 text-green-800' :
                                        doctor.status === 'Busy' ? 'bg-red-100 text-red-800' :
                                        'bg-yellow-100 text-yellow-800'
                                    }`}>
                                        {doctor.status}
                                    </span>
                                </div>
                                <div className="flex justify-between items-center">
                                    <span className="text-sm text-gray-600">Patients Today:</span>
                                    <span className="text-sm font-medium text-gray-900">{doctor.patients}</span>
                                </div>
                            </div>

                            <div className="mt-4 flex space-x-2">
                                <button className="flex-1 bg-blue-50 text-blue-700 hover:bg-blue-100 px-3 py-2 rounded text-sm font-medium">
                                    View Schedule
                                </button>
                                <button className="flex-1 bg-gray-50 text-gray-700 hover:bg-gray-100 px-3 py-2 rounded text-sm font-medium">
                                    Edit
                                </button>
                            </div>
                        </div>
                    ))}
                </div>

                {/* Summary Stats */}
                <div className="mt-8 grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div className="bg-white p-4 rounded-lg shadow-sm border text-center">
                        <div className="text-2xl font-bold text-blue-600">12</div>
                        <div className="text-sm text-gray-600">Total Doctors</div>
                    </div>
                    <div className="bg-white p-4 rounded-lg shadow-sm border text-center">
                        <div className="text-2xl font-bold text-green-600">8</div>
                        <div className="text-sm text-gray-600">Available Now</div>
                    </div>
                    <div className="bg-white p-4 rounded-lg shadow-sm border text-center">
                        <div className="text-2xl font-bold text-yellow-600">3</div>
                        <div className="text-sm text-gray-600">On Break</div>
                    </div>
                    <div className="bg-white p-4 rounded-lg shadow-sm border text-center">
                        <div className="text-2xl font-bold text-red-600">1</div>
                        <div className="text-sm text-gray-600">Busy</div>
                    </div>
                </div>
            </div>
    );
};

export default Page;