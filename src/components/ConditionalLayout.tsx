"use client"

import React from 'react';
import { usePathname } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Sidebar from './Sidebar';

interface ConditionalLayoutProps {
  children: React.ReactNode;
}

const ConditionalLayout: React.FC<ConditionalLayoutProps> = ({ children }) => {
  const pathname = usePathname();
  const { data: session, status } = useSession();
  const router = useRouter();

  // Define routes that should NOT use the dashboard layout
  const authRoutes = ['/auth/login', '/auth/registration', '/auth/password-reset'];
  const publicRoutes = ['/', ...authRoutes];
  
  // Check if current route should use dashboard layout
  const shouldUseDashboardLayout = !publicRoutes.includes(pathname) && pathname !== '/';

  // If it's a protected route, check authentication
  if (shouldUseDashboardLayout) {
    if (status === "loading") {
      return (
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-lg">Loading...</div>
        </div>
      );
    }

    if (status === "unauthenticated") {
      router.push('/auth/login');
      return null;
    }

    // Render with dashboard layout
    return (
      <Sidebar>
        {children}
      </Sidebar>
    );
  }

  // Render without dashboard layout (for auth pages and public routes)
  return <>{children}</>;
};

export default ConditionalLayout;
