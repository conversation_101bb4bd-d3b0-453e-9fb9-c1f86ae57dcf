import React from 'react';

type CreateButton = {
    suffixSting : string,
    icon : string,
    onClick?: () => void
}

const CreateButton = ({suffixSting, icon, onClick}:CreateButton) => {
    return (
        <div>
            <button
                className="btn btn-primary rounded-xl"
                onClick={onClick}
            >
                <i className={icon}></i>
                Add {suffixSting}
            </button>
        </div>
    );
};

export default CreateButton;